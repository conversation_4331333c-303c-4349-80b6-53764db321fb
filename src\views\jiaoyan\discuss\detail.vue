<template>
    <div class="bg">
        <div class="w1280">
            <back>返回</back>
            <!-- 讨论详情 -->
            <div class="boxItem">
                <div class="title">{{ detailData.title || '暂无标题' }}</div>
                <div class="topBox">
                    <div class="userAndtime">
                        <div class="userName">{{ detailData.name || '匿名用户' }}</div>
                        <div class="time">{{ detailData.createTime || '' }}</div>
                    </div>
                    <div class="rightBtn">
                        <div class="btn" @click="toggleLike">
                            <img :src="detailData.isCaseLiked ? '/src/assets/dianzan_sel.png' : '/src/assets/dianzan_def.png'" alt="" class="btnImg">
                            <div class="number">{{ detailData.caseLikedNumber || 0 }}</div>
                        </div>
                        <div class="btn" @click="replyTopic">
                            <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                            <div class="number">{{ detailData.number || 0 }}</div>
                        </div>
                        <div class="btn" v-if="detailData.isCreateAdd" @click="deleteTopic">
                            <img src="@/assets/delete.png" alt="" class="btnImg">
                        </div>
                    </div>
                </div>
                <div class="content" v-if="detailData.content">{{ detailData.content }}</div>
                <div class="imgBox" v-if="detailData.courseTopicList && detailData.courseTopicList.length > 0">
                    <img v-for="(item, index) in detailData.courseTopicList" :key="index" :src="item.url" alt="" class="imgSize">
                </div>
            </div>

            <!-- 排序 -->
            <div class="flex">
                <div class="leftPX">
                    <div class="xuText">排序:</div>
                    <div class="paixu">
                        <el-select
                            v-model="value"
                            clearable
                            placeholder="排序方式"
                            class="select"
                        >
                        <el-option
                            v-for="item in options"
                            :key="item.value"
                            :label="item.label"
                            :value="item.value"
                            />
                        </el-select>
                    </div>
                </div>
                <div class="total">共<span class="replyNumber">118</span>条回复</div>
            </div>
            <!-- 回复列表 -->
            <div class="boxItem">
                <div class="topBox">
                    <div class="userAndtime">
                        <div class="userName">周雨楠</div>
                        <div class="time">2024-09-06 10:24:26</div>
                    </div>
                    <div class="rightBtn">
                        <div class="btn">
                            <img src="@/assets/dianzan_def.png" alt="" class="btnImg">
                            <div class="number">1</div>
                        </div>
                        <div class="btn" @click="openReplyDialog('周雨楠', '1')">
                            <img src="@/assets/pinglun_def.png" alt="" class="btnImg">
                            <div class="number">3</div>
                        </div>
                        <div class="btn">
                            <img src="@/assets/delete.png" alt="" class="btnImg">
                        </div>
                    </div>
                </div>
                <div class="replyContent">第1丰访商学院学前腾主任，今天不在学校，把文件放到了办公室，没办法安排老师去参会1丰访商学院学前腾主</div>
                <div class="imgBox">
                    <img src="@/assets/1.png" alt="" class="imgSize">
                </div>
                <div class="replyBox">
                    <div class="topBox">
                        <div class="userAndtime">
                            <div class="userName">周雨楠</div>
                            <div class="time">2024-09-06 10:24:26</div>
                        </div>
                        <div class="rightBtn">
                            <div class="btn">
                                <img src="@/assets/delete.png" alt="" class="btnImg">
                            </div>
                        </div>
                    </div>
                    <div class="replyContent">第1丰访商学院学前腾主任，今天不在学校，把文件放到了办公室，没办法安排老师去参会1丰访商学院学前腾主</div>
                </div>
            </div>
        </div>

        <!-- 回复话题弹窗 -->
        <ReplyTopic
            ref="replyTopicRef"
            :courseId="courseId"
            :parentId="parentId"
            @refreshList="refreshList"
        />

        <!-- 回复评论弹窗 -->
        <ReplyDialog
            v-model:visible="replyDialogVisible"
            :parentId="currentReplyId"
            :replyName="currentReplyName"
            @refresh="refreshList"
        />
    </div>

</template>
    
<script setup>
import {getMessageDetails} from '@/api/study.js'
import { useRoute } from 'vue-router'
import ReplyTopic from './replyTopic.vue'
import ReplyDialog from './ReplyDialog.vue'

const route = useRoute()
const replyTopicRef = ref()

const value = ref('')
const options = [
  {
    value: '时间顺序',
    label: '时间顺序',
  },
  {
    value: '时间倒序',
    label: '时间倒序',
  },
  {
    value: '点赞数',
    label: '点赞数',
  },
]

const courseId = ref(route.query.courseId || '1')
const parentId = ref('0')
const replyDialogVisible = ref(false)
const currentReplyId = ref('')
const currentReplyName = ref('')

// 详情数据
const detailData = ref({
    caseLikedNumber: 0,
    classId: 0,
    className: '',
    content: '',
    courseTopicList: [],
    createBy: '',
    createTime: '',
    grade: '',
    id: '',
    idNumber: '',
    isCaseLiked: 0,
    isCreateAdd: 0,
    isRecover: 0,
    isReply: 0,
    isTopping: 0,
    name: '',
    nameFaculty: '',
    nameSpeciality: '',
    number: 0,
    numberSum: 0,
    parentId: '0',
    pinNumber: '',
    requestType: 0,
    researchOfficeId: '',
    sort: 0,
    title: '',
    type: 2,
    userType: 0
})

const replyTopic = () => {
  replyTopicRef.value.dialogVisible = true
}

const openReplyDialog = (replyName, replyId) => {
  currentReplyName.value = replyName
  currentReplyId.value = replyId
  replyDialogVisible.value = true
}

const refreshList = () => {
  console.log('刷新列表')
  loadData()
}

const loadData = () => {
    getMessageDetails({
        id: route.query.id
    }).then(res => {
        console.log(res)
        if (res.status === 0 && res.data) {
            detailData.value = res.data
        }
    })
}

// 点赞/取消点赞
const toggleLike = () => {
    // 这里需要调用点赞接口
    console.log('点赞操作')
    // 临时切换状态，实际应该调用接口后更新
    detailData.value.isCaseLiked = detailData.value.isCaseLiked ? 0 : 1
    detailData.value.caseLikedNumber += detailData.value.isCaseLiked ? 1 : -1
}

// 删除话题
const deleteTopic = () => {
    // 这里需要调用删除接口
    console.log('删除话题')
    // 可以添加确认弹窗
}

onMounted(() => {
    loadData()
});
</script>
      
<style lang="scss" scoped>
.bg {
  background: #f5f7fa;
  padding: 31px;
}
.title{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 24px;
    color: #2D2F33;
    margin-bottom: 20px;
}
.boxItem{
    min-height: 149px;
    background: #FFFFFF;
    border-radius: 8px 8px 8px 8px;
    box-sizing: border-box;
    padding: 30px;
    margin-bottom: 20px;
    margin-top: 20px;
}
.topBox{
    display: flex;
    justify-content: space-between;
}
.userAndtime{
    display: flex;
    align-items: center;
}
.userName{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 500;
    font-size: 14px;
    color: #43474D;
    margin-right: 16px;
}
.time{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 13px;
    color: #878D99;
    margin-top: 4px;
}
.rightBtn{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.btn{
    display: flex;
    align-items: center;
    margin-right: 60px;
    cursor: pointer;
}
.btn:last-child{
    margin-right: 0px;
}
.btnImg{
    width: 24px;
    height: 24px;
    margin-right: 8px;
}
.number{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.content{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 16px;
    color: #44474D;
    line-height: 28px;
    margin-top: 12px;
}
.imgBox{
    display: grid;
    gap: 16px;
    grid-template-columns: repeat(5, 1fr);
    margin-top: 16px;
}
.imgSize{
    width: 180px;
    height: 135px;
    border-radius: 8px 8px 8px 8px;
}
.imgSize:last-child{
    margin-right: 0;
}
.flex{
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.leftPX{
    display: flex;
    align-items: center;
}
.xuText{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    margin-right: 5px;
}
.paixu{
    display: flex;
    align-items: center;
}
.total{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #878D99;
}
.replyNumber{
    color:#2D2F33;
}
.replyContent{
    font-family: Source Han Sans CN, Source Han Sans CN;
    font-weight: 400;
    font-size: 14px;
    color: #44474D;
    line-height: 28px;
}


.replyBox{
    min-height: 94px;
    background: #F5F7FA;
    border-radius: 8px 8px 8px 8px;
    margin-top: 16px;
    padding: 20px;
}

</style>
<style lang="scss">
.select {
    .el-select__wrapper{
        width: 180px;
        height: 40px;
        background: #FFFFFF;
        border-radius: 4px 4px 4px 4px;
    }
}
.el-select-dropdown__item {
    padding: 0 !important;
    padding-top: 0 !important;
}

.el-select-dropdown__item:hover {
    background-color: #386CFC !important;
    color: #FFFFFF !important;
}

</style>
  